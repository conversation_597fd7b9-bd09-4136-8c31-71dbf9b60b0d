import {
  login,
  phoneLogin,
  logout,
  findPwdSendCode,
  checkAuthCodeUpdatePwd,
  getMessage,
  getContentByMsgId,
  hasRead,
  removeReaded,
  restoreTrash
} from '@/api/user'
import { setToken, getToken, getFullName, setAccess, setMenuShow, getAccess, setShipCompanyId, setCompanyName, getShipCompanyId, setFullName } from '@/libs/util'

export default {
  state: {
    full_name: getFullName(),
    userId: '',
    avatarImgPath: '',
    token: getToken(),
    conpanyId: getShipCompanyId(),
    canModify: true,
    access: getAccess(),
    menuShow: true,
    hasGetInfo: false,
    unreadCount: 0,
    orgName: '',
    menuList: [],
    messageUnreadList: [],
    messageReadedList: [],
    messageTrashList: [],
    messageContentStore: {}
  },
  mutations: {
    setAvatar (state, avatarPath) {
      state.avatarImgPath = avatarPath
    },
    setUserId (state, id) {
      state.userId = id
    },
    setOrgName (state, name) {
      state.orgName = name
    },
    setUserName (state, name) {
      state.userName = name
    },
    setMenuList (state, arr) {
      state.menuList = arr
    },
    setAccess (state, access) {
      state.access = access
      setAccess(access)
    },
    setMenuShow (state, menuShow) {
      state.menuShow = menuShow
      setMenuShow(menuShow)
    },
    setModify (state, modify) {
      state.canModify = modify
    },
    setToken (state, token) {
      state.token = token
      setToken(token)
    },
    setShipCompanyId (state, ship_company_id) {
      state.ship_company_id = ship_company_id
      setShipCompanyId(ship_company_id)
    },
    setCompanyName (state, company_name) {
      state.company_name = company_name
      setCompanyName(company_name)
    },
    setFullName (state, full_name) {
      state.full_name = full_name
      setFullName(full_name)
    },
    setHasGetInfo (state, status) {
      state.hasGetInfo = status
    },
    setMessageCount (state, count) {
      state.unreadCount = count
    },
    setMessageUnreadList (state, list) {
      state.messageUnreadList = list
    },
    setMessageReadedList (state, list) {
      state.messageReadedList = list
    },
    setMessageTrashList (state, list) {
      state.messageTrashList = list
    },
    updateMessageContentStore (state, { msg_id, content }) {
      state.messageContentStore[msg_id] = content
    },
    moveMsg (state, { from, to, msg_id }) {
      const index = state[from].findIndex(_ => _.msg_id === msg_id)
      const msgItem = state[from].splice(index, 1)[0]
      msgItem.loading = false
      state[to].unshift(msgItem)
    }
  },
  getters: {
    messageUnreadCount: state => state.messageUnreadList.length,
    messageReadedCount: state => state.messageReadedList.length,
    messageTrashCount: state => state.messageTrashList.length
  },
  actions: {
    // 登录
    handleLogin ({ commit }, { userName, password }) {
      userName = userName.trim()
      return new Promise((resolve, reject) => {
        login({
          userName,
          password
        }).then(res => {
          const data = res.data
          if (data.Code === 10000) {
            commit('setToken', data.Token)
            commit('setFullName', data.UserData.user_name)
            commit('setHasGetInfo', true)
            localStorage.setItem('userDataId', data.UserData.unified_account_id)
            localStorage.setItem('userAuth', JSON.stringify(data.Auth))
            localStorage.setItem('hzxUser', data.UserData.hzx_account)
            localStorage.setItem('hzxPwd', data.UserData.hzx_password)
            localStorage.setItem('userData', JSON.stringify(data.UserData))
            localStorage.setItem('userFlow', JSON.stringify(data.PerfDraftFlow))
            localStorage.setItem('mainMenu', JSON.stringify(data.MainMenuList))
            localStorage.setItem('multiToken', data.MultiDataToken.data.tokenType + ' ' + data.MultiDataToken.data.accessToken)
            if (data.UserData.dept_id === '886A1649152E4955B1B0DA4D77745242') { // 公司领导权限特殊处理
              localStorage.setItem('leaderType', 1)
            }
            // 配置登录uuid值 erp登录使用
            // let testPwd = 'y123456'
            let userStr = '{"user":"' + data.UserData.erp_account + '","pwd":"' + data.UserData.erp_password + '"}'
            let uuid = window.btoa(userStr)
            localStorage.setItem('uuid', uuid)
          }
          resolve(data)
        }).catch(err => {
          reject(err)
        })
      })
    },
    // 手机登录
    handlePhoneLogin ({ commit }, { userName }) {
      userName = userName.trim()
      return new Promise((resolve, reject) => {
        phoneLogin({
          userName
        }).then(res => {
          const data = res.data
          if (data.Code === 10000) {
            commit('setToken', data.Token)
            commit('setFullName', data.UserData.user_name)
            commit('setHasGetInfo', true)
            commit('setMenuShow', false)
            localStorage.setItem('userDataId', data.UserData.unified_account_id)
            localStorage.setItem('userAuth', JSON.stringify(data.Auth))
            localStorage.setItem('hzxUser', data.UserData.hzx_account)
            localStorage.setItem('hzxPwd', data.UserData.hzx_password)
            localStorage.setItem('userData', JSON.stringify(data.UserData))
            localStorage.setItem('userFlow', JSON.stringify(data.PerfDraftFlow))
            localStorage.setItem('mainMenu', JSON.stringify(data.MainMenuList))
            if (data.UserData.dept_id === '886A1649152E4955B1B0DA4D77745242') { // 公司领导权限特殊处理
              localStorage.setItem('leaderType', 1)
            }
            // 配置登录uuid值 erp登录使用
            let userStr = '{"user":"' + data.UserData.erp_account + '","pwd":"' + data.UserData.erp_password + '"}'
            let uuid = window.btoa(userStr)
            localStorage.setItem('uuid', uuid)
          }
          resolve(data)
        }).catch(err => {
          reject(err)
        })
      })
    },
    // 退出登录
    handleLogOut ({ state, commit }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('setToken', '')
          commit('setShipCompanyId', '')
          commit('setCompanyName', '')
          commit('setAccess', [])
          commit('setMenuShow', true)
          commit('setFullName', '')
          window.localStorage.clear()
          resolve()
        }).catch(err => {
          reject(err)
        })
      })
    },
    // 更新菜单信息
    handleSetMenuList ({ commit }, arr) {
      commit('setMenuList', arr)
    },
    // 此方法用来获取未读消息条数，接口只返回数值，不返回消息列表
    getUnreadMessageCount ({ state, commit }) {
      // getUnreadCount().then(res => {
      //   const { data } = res
      //   commit('setMessageCount', data)
      // })
    },
    // 获取消息列表，其中包含未读、已读、回收站三个列表
    getMessageList ({ state, commit }) {
      return new Promise((resolve, reject) => {
        getMessage().then(res => {
          const { unread, readed, trash } = res.data
          commit('setMessageUnreadList', unread.sort((a, b) => new Date(b.create_time) - new Date(a.create_time)))
          commit('setMessageReadedList', readed.map(_ => {
            _.loading = false
            return _
          }).sort((a, b) => new Date(b.create_time) - new Date(a.create_time)))
          commit('setMessageTrashList', trash.map(_ => {
            _.loading = false
            return _
          }).sort((a, b) => new Date(b.create_time) - new Date(a.create_time)))
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 根据当前点击的消息的id获取内容
    getContentByMsgId ({ state, commit }, { msg_id }) {
      return new Promise((resolve, reject) => {
        let contentItem = state.messageContentStore[msg_id]
        if (contentItem) {
          resolve(contentItem)
        } else {
          getContentByMsgId(msg_id).then(res => {
            const content = res.data
            commit('updateMessageContentStore', { msg_id, content })
            resolve(content)
          })
        }
      })
    },
    // 把一个未读消息标记为已读
    hasRead ({ state, commit }, { msg_id }) {
      return new Promise((resolve, reject) => {
        hasRead(msg_id).then(() => {
          commit('moveMsg', {
            from: 'messageUnreadList',
            to: 'messageReadedList',
            msg_id
          })
          commit('setMessageCount', state.unreadCount - 1)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 删除一个已读消息到回收站
    removeReaded ({ commit }, { msg_id }) {
      return new Promise((resolve, reject) => {
        removeReaded(msg_id).then(() => {
          commit('moveMsg', {
            from: 'messageReadedList',
            to: 'messageTrashList',
            msg_id
          })
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 还原一个已删除消息到已读消息
    restoreTrash ({ commit }, { msg_id }) {
      return new Promise((resolve, reject) => {
        restoreTrash(msg_id).then(() => {
          commit('moveMsg', {
            from: 'messageTrashList',
            to: 'messageReadedList',
            msg_id
          })
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    }
  }
}
