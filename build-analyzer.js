const fs = require('fs');
const path = require('path');

// 分析项目构建性能的脚本
class BuildAnalyzer {
  constructor() {
    this.srcPath = path.join(__dirname, 'src');
    this.nodeModulesPath = path.join(__dirname, 'node_modules');
  }

  // 分析文件大小
  analyzeFileSize() {
    console.log('🔍 分析文件大小...');
    const largeFiles = [];
    
    this.walkDirectory(this.srcPath, (filePath, stats) => {
      if (stats.size > 100 * 1024) { // 大于100KB的文件
        largeFiles.push({
          path: filePath.replace(__dirname, '.'),
          size: (stats.size / 1024).toFixed(2) + 'KB'
        });
      }
    });

    largeFiles.sort((a, b) => parseFloat(b.size) - parseFloat(a.size));
    console.log('📊 大文件列表 (>100KB):');
    largeFiles.forEach(file => {
      console.log(`  ${file.path}: ${file.size}`);
    });
  }

  // 分析组件数量
  analyzeComponents() {
    console.log('\n🧩 分析组件数量...');
    let vueFiles = 0;
    let jsFiles = 0;
    let totalLines = 0;

    this.walkDirectory(this.srcPath, (filePath, stats) => {
      if (filePath.endsWith('.vue')) vueFiles++;
      if (filePath.endsWith('.js')) jsFiles++;
      
      // 计算代码行数
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        totalLines += content.split('\n').length;
      } catch (e) {
        // 忽略读取错误
      }
    });

    console.log(`📈 统计信息:`);
    console.log(`  Vue组件: ${vueFiles}`);
    console.log(`  JS文件: ${jsFiles}`);
    console.log(`  总代码行数: ${totalLines}`);
  }

  // 分析依赖
  analyzeDependencies() {
    console.log('\n📦 分析依赖...');
    try {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      const deps = Object.keys(packageJson.dependencies || {});
      const devDeps = Object.keys(packageJson.devDependencies || {});
      
      console.log(`生产依赖: ${deps.length}`);
      console.log(`开发依赖: ${devDeps.length}`);
      
      // 检查过时的依赖
      const oldDeps = deps.filter(dep => {
        const version = packageJson.dependencies[dep];
        return version.startsWith('^2.') || version.startsWith('^1.') || version.startsWith('^0.');
      });
      
      if (oldDeps.length > 0) {
        console.log('⚠️  可能过时的依赖:');
        oldDeps.forEach(dep => {
          console.log(`  ${dep}: ${packageJson.dependencies[dep]}`);
        });
      }
    } catch (e) {
      console.log('❌ 无法读取package.json');
    }
  }

  // 遍历目录
  walkDirectory(dir, callback) {
    try {
      const files = fs.readdirSync(dir);
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
          this.walkDirectory(filePath, callback);
        } else if (stats.isFile()) {
          callback(filePath, stats);
        }
      });
    } catch (e) {
      // 忽略访问错误
    }
  }

  // 生成优化建议
  generateRecommendations() {
    console.log('\n💡 优化建议:');
    console.log('1. 升级Node.js到16+版本');
    console.log('2. 升级Vue CLI到4+版本');
    console.log('3. 启用webpack缓存');
    console.log('4. 使用thread-loader进行并行编译');
    console.log('5. 优化代码分割策略');
    console.log('6. 移除不必要的依赖');
    console.log('7. 考虑迁移到Vite构建工具');
  }

  // 运行完整分析
  run() {
    console.log('🚀 开始构建性能分析...\n');
    this.analyzeFileSize();
    this.analyzeComponents();
    this.analyzeDependencies();
    this.generateRecommendations();
    console.log('\n✅ 分析完成!');
  }
}

// 运行分析
const analyzer = new BuildAnalyzer();
analyzer.run();
