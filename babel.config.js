module.exports = {
  presets: [
    '@vue/app',
    [
      '@babel/preset-env',
      {
        useBuiltIns: 'entry',
        // Only transpile what's needed for target browsers
        targets: {
          browsers: [
            '> 1%',
            'last 2 versions',
            'not ie <= 8'
          ]
        },
        // Reduce bundle size
        modules: false,
        // Enable loose mode for faster compilation
        loose: true
      }
    ]
  ],
  // Enable caching for faster rebuilds (handled by babel-loader)
  // cacheDirectory: true,
  // cacheCompression: false,

  ignore: [
    // Ignore large minified files to improve build performance
    '**/dify-chat.js',
    '**/node_modules/**',
    // You can add other large files here if needed
    function(filepath) {
      // Ignore files larger than 500KB
      try {
        const fs = require('fs');
        const stats = fs.statSync(filepath);
        return stats.size > 500 * 1024; // 500KB
      } catch (e) {
        return false;
      }
    }
  ]
}
