import axios from 'axios'
import { getToken, getShipCompanyId } from '@/libs/util'
import router from '../router'
import Cookies from 'js-cookie'

class HttpRequest {
  constructor (baseUrl = baseURL) {
    this.baseUrl = baseUrl
    this.queue = {}
  }
  getInsideConfig () {
    const config = {
      baseURL: this.baseUrl,
      params: {},
      headers: {
        // 'Content-Type': 'application/json',
        // 'Access-Control-Allow-Origin': 'http://erp.xtshipping.net'
      }
    }
    return config
  }
  destroy (url) {
    delete this.queue[url]
    if (!Object.keys(this.queue).length) {
      // Spin.hide()
    }
  }
  interceptors (instance, url) {
    // 请求拦截
    instance.interceptors.request.use(config => {
      // 添加全局的loading...
      if (!Object.keys(this.queue).length) {
        // Spin.show() // 不建议开启，因为界面不友好
      }
      this.queue[url] = true
      // 统一拦截添加token
      // config.params = {}
      if (config.baseURL === 'http://erp.xtshipping.com/' || config.baseURL === 'http://ierptest.xtshipping.com:8022/') { // 'http://ierptest.xtshipping.com:8022/' // 测试线
        config.params.access_token = localStorage.getItem('access_token')
      } else {
        if (router.app.$route.name === 'xt_forms') {
          config.params.token = localStorage.getItem('token')
        } else {
          config.params.token = getToken()
        }
      }
      // 多融合接口拦截添加token
      if (config.baseURL === 'http://************:54321/multi/source/api/' || config.baseURL === 'http://multi.xtshipping.com/multi/source/api/') {
        config.headers.Authorization = localStorage.getItem('multiToken')
        delete config.params.token
      }
      // if (getShipCompanyId() && getShipCompanyId() !== '') {
      //   config.params.ship_company_id = getShipCompanyId()
      // }
      return config
    }, error => {
      return Promise.reject(error)
    })
    // 响应拦截
    instance.interceptors.response.use(res => {
      this.destroy(url)
      const { data, status } = res
      if ((this.baseUrl === 'http://erp.xtshipping.com/' || this.baseUrl === 'http://ierptest.xtshipping.com:8022/') && !res.data.status && res.data.errorCode === '401') { // 'http://ierptest.xtshipping.com:8022/' // 测试线
        localStorage.setItem('access_token', '')
        if (router.app.$route.name !== 'xt_forms') {
          router.push({ name: 'home' })
        }
        return
      }
      // 接口token过期处理
      if (res.data.Code === -202) {
        if (router.app.$route.name !== 'xt_forms') {
          Cookies.set('token', '')
          router.push({ name: 'login', params: { failure: true } })
        }
      }
      return { data, status }
    }, error => {
      this.destroy(url)
      return Promise.reject(error)
    })
  }
  request (options) {
    const instance = axios.create()
    if (options.onUploadProgress) {
      instance.defaults.onUploadProgress = options.onUploadProgress
    }
    options = Object.assign(this.getInsideConfig(), options)
    this.interceptors(instance, options.url)

    return instance(options).catch(error => {
      // Log or handle network errors
      console.error('Network Error:', error.message)
      throw error // Rethrow the error after logging it
    })
  }
}
export default HttpRequest
