<template>
  <Card>
    <Row>
      <Col span="4">
        <Button type="primary" @click="handleAdd">新增</Button>
      </Col>
      <Col span="20" style="text-align: right;">
        <Dropdown @on-click="shipChange">
          <a href="javascript:void(0)" v-html="ship_name !== '' ? ship_name : '请选择'" style="color: #515a6e;"></a>
          <Icon type="md-arrow-dropdown"></Icon>
          <Dropdown-menu slot="list">
            <Dropdown-item v-for="(item, idx) in shipList" :name="item.name" :key="'ship'+idx">{{item.name}}</Dropdown-item>
          </Dropdown-menu>
        </Dropdown>
        <span style="line-height: 33px;">共{{total}}条结果</span>
        <search @searchResults="searchResults" :setSearch="setSearchData" @resetResults="resetResults" class="search-vie"></search>
      </Col>
    </Row>
    <Row>
      <Col span="4" style="border: 1px solid #ccc;">
        <h3 class="col-text">船舶备件</h3>
        <Tree :data="equipTree" :render="renderContent" class="tree" @on-select-change="treeSelect" @on-toggle-expand="toggleExpand"></Tree>
      </Col>
      <Col span="20" style="padding-left: 10px;">
        <Table
          border
          ref="selection"
          :loading="loading"
          :columns="columns"
          :data="equipList"
          @on-row-click="tableClick"></Table>
        <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      </Col>
    </Row>
    <!-- 弹窗内容 -->
    <div>
      <Modal v-model="modal" :title="titleModal" width="70%" :mask-closable="false" @on-visible-change="modalShowHide">
        <div class="ship_title">{{ ship_name }}</div>
        <Form ref="formInline" :model="formInline" :rules="ruleInline" inline :label-width="100">
          <div v-if="modalType !== 'file'" class="form-class-div">
            <Row>
              <Col span="12">
                <Form-item prop="name" label="备件名称">
                  <Input type="text" v-model="formInline.name" placeholder="请输入"></Input>
                </Form-item>
              </Col>
              <Col span="12">
                <Form-item label="系统编码">
                  <div>{{ formInline.oldnumber || '--' }}</div>
                </Form-item>
              </Col>
            </Row>
            <Row                    >
              <Col span="12">
                <Form-item label="备件号">
                  <Input type="text" v-model="formInline.xtgf_sparenumber" placeholder="请输入"></Input>
                </Form-item>
              </Col>
              <Col span="12">
                <Form-item label="单位" prop="baseunit">
                  <Select filterable v-model="formInline.baseunit" label-in-value @on-select-change="seleClick" :disabled="modalType === 'modify'">
                    <Option v-for="(item, idx) in unitList" :key="'unit' + idx" :value="item.number">{{ item.name }}</Option>
                  </Select>
                </Form-item>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <Form-item prop="group" label="所属设备" class="self_equip">
                  <SelectTree v-model="formInline.group" :treeData="equipTypeList" :showQuery="false" @on-select-change="selectTreeChange"></SelectTree>
                </Form-item>
              </Col>
              <Col span="12">
                <Form-item label="规格">
                  <Input type="text" v-model="formInline.modelnum" placeholder="请输入"></Input>
                </Form-item>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <Form-item label="制造厂商">
                  <Input type="text" v-model="formInline.xtgf_maker" placeholder="请输入"></Input>
                </Form-item>
              </Col>
              <Col span="12">
                <Form-item label="供应商">
                  <Input type="text" v-model="formInline.xtgf_vendor" placeholder="请输入"></Input>
                </Form-item>
              </Col>
            </Row>
            <Row>
              <Form-item label="备注">
                <Input type="textarea" v-model="formInline.xtgf_remark" :rows="1" placeholder="请输入"></Input>
              </Form-item>
            </Row>
          </div>
          <!-- <div class="tr-name" v-if="modalType !== 'file'">附件</div> -->
          <!-- <fileUpload ref="fileUploadComponent" :fileDataList="fileDataList" type="modalType" @getFileId="getFileId"></fileUpload> -->
        </Form>
        <div slot="footer">
          <Button type="primary" @click="handleCancel">取消</Button>
          <Button type="primary" @click="handleSubmit('formInline')">保存</Button>
        </div>
      </Modal>
    </div>
  </Card>
</template>
<script>
import SelectTree from 'iview-select-tree'
import search from '_c/search'
import fileUpload from '../../../performance/performanceTemp/fileUpload'
import API from '@/api/erpSys/common'
import { shipQuery, equipListLevelThree, equipListLevelMore, equipDetailGoodSelect, equipDetailGoodSearch } from '@/api/erpSys/equipManager'

export default {
  components: {
    SelectTree,
    search,
    fileUpload
  },
  data () {
    return {
      visible: false,
      unit_name: '',
      preNumber: '02', // 保留查询前的搜索number
      formItemWidth: null,
      ship_name: '',
      shipList: [], // 储存船名下拉
      equip_name: '',
      equipTree: [],
      setSearchData: {
        name: {
          type: 'text',
          width: 220,
          value: '',
          isdisable: false,
          placeholder: '请输入设备名称、设备型号、制造厂商'
        }
      },
      loading: false,
      total: 0,
      classiQuery: {
        number: '02',
        createorg_number: 'xt',
        level: '2',
        status: 'C', // ['A', 'B', 'C'],
        pageSize: 10000,
        pageNo: 1
      },
      listQuery: {
        number: '02',
        createorg_number: 'xt',
        level: '2',
        status: 'C', // ['A', 'B', 'C'],
        pageSize: 10,
        pageNo: 1
      },
      listCurrent: 1,
      equipList: [], // 设备列表
      columns: [
        {
          type: 'index',
          title: '序号',
          width: 70,
          align: 'center'
        },
        {
          title: '备件名称',
          key: 'name',
          align: 'center'
        },
        {
          title: '备件号',
          key: 'xtgf_sparenumber',
          align: 'center'
        },
        {
          title: '单位',
          key: 'baseunit_name',
          align: 'center'
        },
        {
          title: '规格型号',
          key: 'modelnum',
          align: 'center'
        },
        {
          title: '制造厂商',
          key: 'xtgf_maker',
          align: 'center'
        },
        {
          title: '数据状态',
          key: 'status',
          align: 'center',
          width: 100,
          render: (h, params) => {
            let curVal = ''
            switch (params.row.status) {
              case 'A':
                curVal = '暂存'
                break
              case 'B':
                curVal = '已提交'
                break
              case 'C':
                curVal = '已审核'
                break
              default:
                break
            }
            return h('div', {}, curVal)
          }
        }
        // {
        //   title: '操作',
        //   key: '',
        //   width: 100,
        //   align: 'center',
        //   render: (h, params) => {
        //     return h('Button', {
        //       props: {
        //         icon: 'md-trash',
        //         size: 'small'
        //       },
        //       on: {
        //         click: (e) => {
        //           e.stopPropagation()
        //           this.handleDelete(params.row)
        //         }
        //       }
        //     })
        //   }
        // }
      ],
      modal: false,
      titleModal: '',
      formInline: {
        group: ''
      },
      ruleInline: {
        name: [{ required: true, message: '此处不能为空!', trigger: 'change' }],
        group: [{ required: true, message: '此处不能为空!', trigger: 'change' }],
        baseunit: [{ required: true, message: '此处不能为空!', trigger: 'blur' }]
      },
      unitList: [],
      equipTypeList: [],
      modalType: '',
      equipTreeSeled: {}, // 当前已选中的节点数组
      fileDataList: [],
      wps_ids: ''
    }
  },
  methods: {
    selectTreeChange (val) {
      this.formInline.group = val
      if (val.length === 6) {
        this.$Message.warning('请重新选择所属设备')
      }
    },
    // 获取下拉所属设备信息
    getEquipDropTree () {
      let data = {
        level: '3',
        name: this.ship_name,
        enable: '1',
        pageSize: 10000,
        pageNo: 1
      }
      equipListLevelThree(data).then(res => { // 船舶设备一级列表-通过船舶名称进行查询
        if (res.status === 200) {
          if (res.data.data.rows.length > 0) {
            res.data.data.rows.forEach((e, idx) => {
              /**
               * 以下为防止多渲染做的无奈操作
               */
              if (this.equipTypeList.length < res.data.data.rows.length) {
                this.equipTypeList.push({})
              } else {
                this.equipTypeList.push({ title: 'test' })
                this.equipTypeList.pop()
              }
              Object.assign(this.equipTypeList[idx], {
                title: e.parent_name,
                value: e.number
              })
              /**
               * 以上为防止多渲染做的无奈操作
               */
              let _secData = {
                number: e.number,
                level: '4',
                status: this.listQuery.status,
                pageSize: 10000,
                pageNo: 1
              }
              equipListLevelMore(_secData).then(secRes => { // 二级设备获取
                if (secRes.data.data.rows.length > 0) {
                  Object.assign(this.equipTypeList[idx], {
                    children: []
                  })
                  secRes.data.data.rows.forEach((item, secIdx) => {
                    this.equipTypeList[idx].children.push({
                      title: item.name,
                      value: item.number
                    })
                    let _thirdData = {
                      number: item.number,
                      level: '5',
                      status: this.listQuery.status,
                      pageSize: 10000,
                      pageNo: 1
                    }
                    equipListLevelMore(_thirdData).then(thirdRes => { // 三级设备获取
                      if (thirdRes.data.data.rows.length > 0) {
                        Object.assign(this.equipTypeList[idx].children[secIdx], {
                          children: []
                        })
                        thirdRes.data.data.rows.forEach((list, thirdIdx) => {
                          this.equipTypeList[idx].children[secIdx].children.push({
                            title: list.name,
                            value: list.number
                          })
                        })
                      }
                    })
                  })
                }
              })
            })
          }
        }
      })
    },
    // 获取船舶设备
    getEquipListLevelThree () {
      this.equipTree = []
      let data = {
        level: '3',
        name: this.ship_name,
        enable: '1',
        pageSize: 10000,
        pageNo: 1
      }
      equipListLevelThree(data).then(res => { // 船舶设备一级列表-通过船舶名称进行查询
        if (res.status === 200) {
          if (res.data.data.rows.length > 0) {
            this.equipTreeSeled.parent_parent_name = res.data.data.rows[0].parent_name
            this.equipTreeSeled.parent_name = '--'
            this.equipTreeSeled.number = res.data.data.rows[0].number
            this.formInline.group = res.data.data.rows[0].number
            this.getSelectList(res.data.data.rows[0], 0)
            res.data.data.rows.forEach((e, idx) => {
              this.equipTree.push({
                title: e.parent_name,
                parent_parent_name: e.parent_name,
                parent_name: '--',
                number: e.number,
                selected: idx === 0 ? true : undefined,
                render: (h, { root, node, data }) => { // root根节点, node当前节点, data
                  return h('span', {
                    style: {
                      display: 'inline-block',
                      width: '100%'
                    },
                    on: {
                      click: () => {
                        this.listQuery.pageNo = 1
                        this.getSelectList(e, idx)
                      }
                    }
                  }, [
                    h('span', [
                      h('Icon', {
                        props: {
                          type: 'ios-home-outline'
                        },
                        style: {
                          marginRight: '8px'
                        },
                        on: {
                          click: () => {
                            this.getSelectList(e, idx)
                          }
                        }
                      }),
                      h('span', data.title)
                    ])
                  ])
                },
                children: [{
                  title: ''
                }]
              })
            })
          }
        }
      })
    },
    // 关闭弹窗
    modalShowHide (val) {
      if (!val) {
        this.handleCancel()
      } else {
        console.log(this.formInline)
      }
    },
    // 左侧点击查询列表详情
    getSelectList (row, idx) {
      if (row) {
        this.listQuery.number = row.number
      }
      delete this.listQuery.level
      equipDetailGoodSelect(this.listQuery).then(res => { // 获取备件列表详情
        if (res.data.status) {
          this.equipList = res.data.data.rows
          this.total = res.data.data.totalCount
        }
      })
    },
    // 搜索框输入查询列表详情
    getSearchList () {
      this.preNumber = this.listQuery.number
      Object.assign(this.listQuery, { // 2023年10月8日加
        group_name: this.ship_name,
        group_parent_name: this.ship_name,
        group_parent_fullname: this.ship_name + '!'
      })
      this.listQuery.number = '02'
      equipDetailGoodSearch(this.listQuery).then(res => { // 获取备件列表详情
        if (res.data.status) {
          this.equipList = res.data.data.rows
          this.total = res.data.data.totalCount
        } else {
          this.$Message.error(res.data.message)
        }
      })
    },
    getSecLeveList (row, idx) {
      this.listQuery.number = row.number
      delete this.listQuery.level
      equipListLevelMore(this.listQuery).then(res => { // 获取设备二级
        if (res.data.status) {
          this.equipList = res.data.data.rows
          this.total = res.data.data.totalCount
        }
      })
    },
    // 获取三级设备
    getTriLeveList (row, idx, tidx) {
      this.listQuery.number = row.number
      this.listQuery.level = 5
      equipListLevelMore(this.listQuery).then(res => { // 获取设备三级
        if (res.data.status) {
          this.equipList = res.data.data.rows
          this.total = res.data.data.totalCount
        }
      })
    },
    // 获取设备列表
    getequipList (d) {
      this.loading = true
      equipListLevelMore(this.listQuery).then(res => { // 获取设备列表
        this.loading = false
        if (res.data.status) {
          this.equipList = res.data.data.rows
          this.total = res.data.data.totalCount
        }
      })
    },
    // 树节点点击事件
    treeSelect (row) {
      if (row.length > 0) {
        this.equipTreeSeled.parent_parent_name = row[0].parent_parent_name
        this.equipTreeSeled.parent_name = row[0].parent_name
        this.equipTreeSeled.number = row[0].number
        this.formInline.group = row[0].number
      }
    },
    toggleExpand (row) {
      if (row.number.length > 8) return
      this.listQuery.number = row.number
      this.listQuery.level = row.number.length > 6 ? 5 : 4
      let data = {
        number: row.number,
        level: row.number.length > 6 ? 5 : 4,
        status: this.listQuery.status,
        pageSize: 10000,
        pageNo: 1
      }
      equipListLevelMore(data).then(res => { // 获取设备二级
        if (res.data.status) {
          // this.equipList = res.data.data.rows
          if (res.data.data.rows.length > 0) {
            row.children = []
            res.data.data.rows.forEach((item, idx) => {
              if (row.number.length > 6) {
                row.children.push({
                  parent_parent_name: item.parent_parent_name,
                  parent_name: item.name,
                  title: item.name,
                  number: item.number,
                  render: (h, { root, node, data }) => { // root根节点, node当前节点, data
                    return h('span', {
                      style: {
                        display: 'inline-block',
                        width: '100%'
                      },
                      on: {
                        click: () => {
                          this.listQuery.pageNo = 1
                          this.listQuery.number = item.number
                          this.getSelectList(data)
                        }
                      }
                    }, [
                      h('span', [
                        h('span', data.title)
                      ])
                    ])
                  }
                })
              } else {
                row.children.push({
                  parent_parent_name: item.parent_parent_name,
                  parent_name: item.name,
                  title: item.name,
                  number: item.number,
                  render: (h, { root, node, data }) => { // root根节点, node当前节点, data
                    return h('span', {
                      style: {
                        display: 'inline-block',
                        width: '100%'
                      },
                      on: {
                        click: () => {
                          this.listQuery.pageNo = 1
                          this.listQuery.number = item.number
                          this.getSelectList(data)
                        }
                      }
                    }, [
                      h('span', [
                        h('span', data.title)
                      ])
                    ])
                  },
                  children: [{
                    title: ''
                  }]
                })
              }
            })
          } else {
            row.children = [{
              title: '暂无数据',
              render: (h, { root, node, data }) => {
                return h('span', {}, '暂无数据')
              }
            }]
          }
        }
      })
    },
    // 树节点
    renderContent (h, { root, node, data }) {
      return h('span', {
        style: {
          display: 'inline-block',
          width: '100%'
        },
        on: {
          click: () => {}
        }
      }, [
        h('span', [
          h('Icon', {
            props: {
              type: 'ios-folder-outline'
            },
            style: {
              marginRight: '8px'
            }
          }),
          h('span', data.title)
        ])
      ])
    },
    changeTableColumns () {
      this.tableColumns2 = this.getTable2Columns()
    },
    // 船名搜索
    shipChange (name) {
      this.ship_name = name
      localStorage.setItem('equip_ship_name', name)
      this.ship_id = this.shipList.filter(item => { return item.name === name })[0].number
      this.equipTree = []
      this.getEquipDropTree()
      this.getEquipListLevelThree()
    },
    // 备件查询
    equipChange (name) {
      this.equip_name = name
    },
    // 新增
    handleAdd () {
      this.formInline.group = this.equipTreeSeled.number
      if (this.formInline.group.length === 6) {
        this.$Message.warning('请重新选择所属设备再新增')
        return
      }
      this.getUnitList()
      this.getEquipDropTree()
      this.modalType = 'add'
      this.titleModal = '新增备件基本信息' + ' (' + this.ship_name + ')'
      this.modal = true
    },
    // 查询
    searchResults (e) {
      this.listCurrent = 1
      this.listQuery.pageNo = 1
      Object.assign(this.listQuery, {
        name: this.setSearchData.name.value
      })
      this.getSearchList()
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery.pageSize = 10
      this.listQuery.pageNo = 1
      this.setSearchData.name.value = ''
      Object.assign(this.listQuery, {
        number: this.preNumber,
        name: this.setSearchData.name.value
      })
      this.getSelectList(this.listQuery)
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      if (this.setSearchData.name.value !== '') {
        this.getSearchList()
      } else {
        this.getSelectList()
      }
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageNo = val
      if (this.setSearchData.name.value !== '') {
        this.getSearchList()
      } else {
        this.getSelectList()
      }
    },
    // 保存
    handleSubmit (name) {
      if (this.formInline.group.length === 6) {
        this.$Message.warning('请重新选择所属设备')
        this.formInline.group = ''
        return
      }
      this.formInline.groupid_number = this.formInline.group
      this.formInline.standardid_number = 'JBFLBZ'
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>确认保存备件手册？</p>',
            loading: true,
            onOk: () => {
              if (this.modalType === 'add') { // 新增
                let date = new Date()
                let curyear = date.getFullYear()
                let curmonth = date.getMonth() < 10 ? '0' + (date.getMonth() + 1) : date.getMonth()
                let curday = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
                let curhour = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
                let curminutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
                let curseconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
                let curDate = curyear + '-' + curmonth + '-' + curday + ' ' + curhour + ':' + curminutes + ':' + curseconds
                let data = {
                  materialsList: [{
                    name: this.formInline.name,
                    createorg: { number: '100.01' },
                    baseunit: { number: this.formInline.baseunit },
                    creator: { number: 'ID-000023' },
                    createtime: curDate,
                    modifytime: curDate,
                    description: '',
                    ctrlstrategy: 2,
                    materialtype: 1,
                    status: 'C',
                    modelnum: this.formInline.modelnum,
                    xtgf_sparenumber: this.formInline.xtgf_sparenumber,
                    xtgf_remark: this.formInline.xtgf_remark,
                    xtgf_vendor: this.formInline.xtgf_vendor,
                    xtgf_maker: this.formInline.xtgf_maker,
                    serviceattribute: { number: '1001' },
                    enablepur: true,
                    enablesale: true,
                    enableinv: true,
                    xtgf_ispushcalinfo: true,
                    group: { number: this.formInline.group },
                    entry_groupstandard: [{
                      groupid: { number: this.formInline.group },
                      standardid: { number: 'JBFLBZ' }
                    }]
                  }]
                }
                let dataParam = {
                  data: JSON.stringify(data),
                  url: '/ierp/kapi/v2/basedata/generatemtbizinfo'
                }
                API.transferStation(dataParam).then(res => {
                  if (res.data.Result.status || res.data.Result.data[0].message.includes('请至少修改一项')) { // 审核通过 直接走流程
                    this.loading = false
                    this.$Modal.remove()
                    // this.getEquipListLevelThree()
                    this.handleCancel()
                    if (this.setSearchData.name.value !== '') {
                      this.getSearchList()
                    } else {
                      this.getSelectList()
                    }
                  } else if (res.data.Result.status || res.data.Result.data[0].message.includes('单据已在流程中流转，不能审核')) { // 提交未审核，再次请求审核
                    let dataId = {
                      data: {
                        id: res.data.Result.data[0].materialId
                      }
                    }
                    let dataParam = {
                      data: JSON.stringify(dataId),
                      url: '/ierp/kapi/v2/xtgf/basedata/bd_material/goodAudit'
                    }
                    API.transferStation(dataParam).then(res => {
                      if (res.data.Result.status) {
                        this.$Message.success('新增并审核成功。')
                        this.loading = false
                        this.$Modal.remove()
                        // this.getEquipListLevelThree()
                        this.handleCancel()
                        if (this.setSearchData.name.value !== '') {
                          this.getSearchList()
                        } else {
                          this.getSelectList()
                        }
                      }
                    })
                  } else {
                    this.loading = false
                    this.$Modal.remove()
                    this.$Message.error(res.data.Message)
                  }
                })
              }
              if (this.modalType === 'modify') { // 修改
                let dataId = {
                  data: {
                    id: this.formInline.id
                  }
                }
                let backExamDataParam = {
                  data: JSON.stringify(dataId),
                  url: '/ierp/kapi/v2/xtgf/basedata/bd_material/goodUnAudit'
                }
                API.transferStation(backExamDataParam).then(res => { // 反审核
                  if (res.data.Result.status) {
                    this.formInline.status = 'A'
                    let _data = {
                      data: this.formInline
                    }
                    let dataParam = {
                      data: JSON.stringify(_data),
                      url: '/ierp/kapi/v2/xtgf/basedata/bd_material/goodAddEdit'
                    }
                    API.transferStation(dataParam).then(res => { // 修改
                      this.$Modal.remove()
                      if (res.data.Result.status) {
                        this.submitSort(this.formInline.id, 'modify')
                      } else {
                        this.$Message.warning('修改信息失败')
                      }
                    })
                  }
                })
              }
            }
          })
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 备件提交、审核
    submitSort (id, type) {
      let dataId = {
        data: {
          id: id
        }
      }
      let dataParam = {
        data: JSON.stringify(dataId),
        url: '/ierp/kapi/v2/xtgf/basedata/bd_material/goodSubmit'
      }
      API.transferStation(dataParam).then(res => {
        if (res.data.Result.status) {
          let dataParam = {
            data: JSON.stringify(dataId),
            url: '/ierp/kapi/v2/xtgf/basedata/bd_material/goodAudit'
          }
          API.transferStation(dataParam).then(res => {
            if (res.data.Result.status) {
              if (type === 'add') this.$Message.success('新增成功。')
              if (type === 'modify') this.$Message.success('修改成功。')
              this.handleCancel()
              if (this.setSearchData.name.value !== '') {
                this.getSearchList()
              } else {
                this.getSelectList()
              }
            }
          })
        } else {
          this.$Message.warning('信息提交失败')
        }
      })
    },
    handleCancel () {
      this.modalType = ''
      this.unit_name = ''
      let _group = this.formInline.group // 临时变量，保证备件类别不被置空
      this.$refs['formInline'].resetFields()
      this.formInline = {}
      this.formInline.group = _group
      this.unitList = []
      this.visible = false
      setTimeout(() => {
        this.modal = false
      }, 100)
    },
    // 编辑
    tableClick (list) {
      this.modal = true
      this.modalType = 'modify'
      this.titleModal = '编辑备件基本信息' + ' (' + this.ship_name + ')'
      this.formInline = list
      this.formInline.group = list.group_number
      this.formInline.baseunit = list.baseunit_number
      this.unit_name = list.baseunit_name
      this.getEquipDropTree()
      this.getUnitList()
    },
    // 删除列表
    handleDelete (row) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>删除后无法恢复，是否确认删除？</p>',
        loading: true,
        onOk: () => {
          // API.({ id: row.id }).then(res => {
          //   if (res.data.Code === 10000) {
          //     this.loading = false
          //     this.$Modal.remove()
          //     this.$Message.success(res.data.Message)
          //     this.getList(this.equipTree[0].id)
          //   } else {
          //     this.loading = false
          //     this.$Modal.remove()
          //     this.$Message.error(res.data.Message)
          //   }
          // })
        }
      })
    },
    // 附件上传
    handleUpload (d) {},
    getFileId () {
      this.wps_ids = this.fileDataList.map(item => {
        return item.id
      }).join()
    },
    // 获取计量单位
    getUnitList () {
      let data = {
        enable: '1',
        status: 'C',
        pageSize: '10000',
        pageNo: '1'
      }
      API.getUnitList(data).then(res => {
        if (res.status === 200) {
          res.data.data.rows.map(item => {
            this.unitList.push({
              isEdit: true,
              name: item.name,
              number: item.number
            })
          })
        }
      })
    },
    // 点击单位下拉项触发
    seleClick (item) {
      this.unit_name = item.label
      this.formInline.baseunit = item.value
    }
  },
  created () {
    shipQuery({ pageSize: 10000, pageNo: 1 }).then(res => { // 获取船舶列表
      if (res.status === 200) {
        this.shipList = res.data.data.rows
        this.ship_id = this.shipList[0].number
        if (localStorage.getItem('equip_ship_name')) {
          this.ship_name = localStorage.getItem('equip_ship_name')
          this.ship_id = this.shipList.filter(item => { return item.name === this.ship_name })[0].number
        } else {
          this.ship_name = this.shipList[0].name
        }
        this.getEquipDropTree()
        this.getEquipListLevelThree()
      }
    })
  }
}
</script>
<style lang="less" scoped>
.drop_down_sel {
  width: 200px;
  height: 32px;
  padding: 0 10px;
  border-radius: 5px;
  border: 1px solid #dcdee2;
  .ivu-icon-md-arrow-dropdown {
    position: absolute;
    margin-top: 9px;
  }
  a {
    color: #666;
    width: 95%;
    display: inline-block;
  }
}
.drop_down_sel.sel_focux {
  border: 1px solid #2D8cF0;
}
.positon-span {
  text-align: right;
  margin-left: 5px;
  i {
    padding: 0 5px;
    font-size: 16px;
  }
}
.search-vie {
  display: inline-block;
  vertical-align: middle;
  margin-left: 5px;
}
.col-text {
  line-height: 40px;
  border-bottom: 1px solid #ccc;
  font-weight: 700;
  font-size: 16px;
  color: #000;
  text-align: center;
}
.tr-name {
  font-size: 16px;
  color: #333;
  font-weight: 700;
  margin: 15px 0;
  &&::before {
    content: "";
    font-size: 16px;
    padding-right: 10px;
    border-left: 4px solid #1d6ced;
  }
}
.form-class-div {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
}
</style>
<style>
.ship_title {
  position: absolute;
  font-size: 20px;
  top: 10px;
  display: inline-block;
  text-align: center;
  width: 100%;
  font-weight: bold;
  color: #17233d;
}
.form-class-div .ivu-form-item-error-tip {
  z-index: 2 !important;
  padding-left: 15px;
}
.tree {
  max-height: 600px;
  overflow: auto;
}
.tree.ivu-tree ul li {
  margin: 5px 0;
}
.tree .ivu-tree-title span {
  font-size: 16px;
  color: #666;
}
.ivu-form .form-class-div .ivu-col.ivu-col-span-12, .ivu-form .form-class-div > .ivu-row > .ivu-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
}
.ivu-form .form-class-div .ivu-form-item-label {
  background-color: #eee;
  padding: 0 8px;
  font-size: 12px;
  font-weight: 700;
  color: #333;
  display: inline-block;
  line-height: 40px;
  border-right: 1px solid #ccc;
}
.ivu-form .form-class-div .remak_la .ivu-form-item-label {
  line-height: 58px;
}
.form-class-div .ivu-form-item {
  margin: 0;
  line-height: 40px;
  width: 100%;
}
.ivu-form .form-class-div .ivu-form-item-content {
  padding: 0 10px;
  margin-top: 3px;
  height: 32px;
}
.ivu-form .form-class-div .ivu-form-item-content .ivu-dropdown {
  width: 100%;
}
.form-class-div .ivu-input-number {
  width: 100%;
}
.self_equip .ivu-select-placeholder, .self_equip .ivu-select-selected-value {
  padding-left: 0 !important;
}
</style>
